%% 肠鸣音音频数据可视化程序
%PHOTOGRAPH 肠鸣音音频数据可视化分析脚本
%   该脚本用于加载并可视化timetable格式的肠鸣音音频数据，生成专业的
%   波形图、频谱图和强度图，用于医学研究中的肠鸣音信号分析。
%
%   功能特性:
%   - 交互式文件选择界面
%   - 智能timetable变量识别和用户选择
%   - 自动数据格式验证和错误处理
%   - 三种专业可视化图表生成
%   - 科研级别的图形质量和格式
%
%   数据要求:
%   - 输入文件: .mat格式文件
%   - 数据变量: 任何timetable格式的变量（优先选择包含'tt1'的变量）
%   - 采样频率: 2570 Hz (固定)
%   - 数据列数: 单列音频信号数据
%
%   输出图表:
%   1. 波形图 - 显示原始音频信号的时域特征
%   2. 频谱图 - 显示信号的时频域分布特征
%   3. 强度图 - 显示肠鸣音在100-1000Hz频段的强度变化
%
%   处理流程:
%   1. 用户通过文件对话框选择.mat数据文件
%   2. 自动识别文件中的所有timetable变量
%   3. 如有多个变量，提供交互式选择界面供用户选择
%   4. 验证选定数据的格式和timetable结构完整性
%   5. 提取时间序列和信号数据，执行时间归零处理
%   6. 调用专业绘图函数生成三种类型的可视化图表
%   7. 应用统一的科研级别图形格式和样式
%
%   技术参数:
%   - 采样频率: 2570 Hz
%   - 图形尺寸: 1500×900 像素
%   - 字体设置: Times New Roman, 加粗
%   - 时间范围: 动态适应数据长度
%   - 强度频段: 100-1000 Hz
%
%   依赖函数:
%   - plot_waveform: 波形图绘制函数
%   - plot_spectrogram: 频谱图绘制函数
%   - plot_intensity: 强度图绘制函数
%   - inferno: 感知均匀颜色映射函数
%
%   应用场景:
%   - 肠鸣音信号的快速可视化分析
%   - 音频数据质量检查和评估
%   - 医学研究中的信号特征观察
%   - 科研论文图形制作和展示
%
%   使用示例:
%   1. 运行脚本: photograph
%   2. 在弹出的文件对话框中选择包含timetable数据的.mat文件
%   3. 如果文件包含多个timetable变量，在命令行中选择要处理的变量
%   4. 程序自动生成三个可视化图表窗口
%
%   注意事项:
%   - 确保function文件夹位于上级目录
%   - 数据文件必须包含至少一个timetable格式的变量
%   - 多个变量时程序会提供交互式选择界面
%   - 程序自动适应任意长度的数据，无需固定为5分钟
%
%   错误处理:
%   - 文件选择取消检测
%   - timetable变量存在性验证
%   - 用户输入验证和重新提示
%   - timetable格式完整性检查
%
%   参见: PLOT_WAVEFORM, PLOT_SPECTROGRAM, PLOT_INTENSITY, TIMETABLE
%
%   作者: [医学信号处理团队]
%   创建日期: [创建日期]
%   最后修改: [修改日期]
%   版本: 1.0

% 清空工作空间并关闭所有图形窗口
clear all;
close all;

% 添加函数路径 - function文件夹在上级目录
addpath('../function');

% 加载数据文件 - 让用户选择文件
[filename, pathname] = uigetfile('*.mat', '请选择包含timetable数据的.mat文件');

% 检查用户是否取消了文件选择
if isequal(filename, 0)
    disp('用户取消了文件选择');
    return;
end

% 构建完整的文件路径并加载
fullpath = fullfile(pathname, filename);
fprintf('正在加载文件: %s\n', fullpath);
load(fullpath);

% 动态检查加载的数据中的timetable变量
loaded_vars = who; % 获取所有加载的变量名
timetable_vars = {};
timetable_var_name = '';

% 查找所有timetable类型的变量
for i = 1:length(loaded_vars)
    var_name = loaded_vars{i};
    if exist(var_name, 'var') && istimetable(eval(var_name))
        timetable_vars{end+1} = var_name;
    end
end

% 检查是否找到timetable变量
if isempty(timetable_vars)
    error('在数据文件中找不到任何timetable格式的变量');
elseif length(timetable_vars) == 1
    % 只有一个timetable变量，直接使用
    timetable_var_name = timetable_vars{1};
    fprintf('找到timetable变量: %s\n', timetable_var_name);
else
    % 多个timetable变量，让用户选择
    fprintf('\n发现多个timetable变量:\n');
    for i = 1:length(timetable_vars)
        var_name = timetable_vars{i};
        var_data = eval(var_name);
        time_duration = seconds(var_data.Time(end) - var_data.Time(1));
        fprintf('  [%d] %s (数据点: %d, 时长: %.1f秒)\n', ...
            i, var_name, height(var_data), time_duration);
    end

    % 用户选择
    while true
        choice = input(sprintf('\n请选择要处理的变量 (1-%d): ', length(timetable_vars)));

        % 检查输入是否有效
        if isnumeric(choice) && isscalar(choice) && ...
           choice >= 1 && choice <= length(timetable_vars) && ...
           choice == round(choice)
            timetable_var_name = timetable_vars{choice};
            fprintf('您选择了: %s\n', timetable_var_name);
            break;
        else
            fprintf('无效选择，请输入1到%d之间的整数。\n', length(timetable_vars));
        end
    end
end

% 获取选定的timetable变量
selected_tt = eval(timetable_var_name);

% 从timetable中提取时间和信号数据
time = selected_tt.Time - selected_tt.Time(1); % 将时间归零
signal = selected_tt.Variables; % 假设timetable只有一列数据

% 设置采样频率
fs = 2570; % 采样频率

% 计算实际数据的时间范围
time_start = 0; % 时间已归零，起始时间为0
time_end = seconds(time(end)); % 结束时间（秒）
time_range = [time_start, time_end];

% 显示数据信息
fprintf('数据时间范围: %.1f - %.1f 秒 (总时长: %.1f 秒)\n', ...
    time_start, time_end, time_end - time_start);

% 绘制波形图
position = [200, 200, 1500, 900]; % [left, bottom, width, height]

plot_waveform(time, signal, position, 2, 1, 1, 'Raw data of Mic (Body)', time_range);

% 绘制频谱图
plot_spectrogram(signal, fs, position, 2, 1, 2, 'Spectrogram of Mic (Body)', time_range);

% 显示强度图
plot_intensity(time, signal, fs, position, 2, 1, 1, 'Intensity of Mic (Body)', time_range);




