# 数据合并脚本使用说明

## 概述
`merge_data_files.m` 脚本用于将 `1、Raw data` 文件夹中的多个时间表（timetable）数据文件按照特定顺序合并成连续的时间序列。

## 功能特性
- **自动文件扫描**：自动识别Raw data文件夹中的所有.mat文件
- **智能排序**：按照文件名中的数字序号和段号进行排序
- **时间连续性**：确保合并后的时间序列连续且有序
- **多变量支持**：同时处理tt1和tt2两种类型的时间表变量
- **自动保存**：将合并结果保存到Processed data文件夹

## 排序规则
1. **主排序**：按文件名中的数字序号排序（如：data3 → data4）
2. **次排序**：按文件内的段号排序（如：seg001 → seg002 → seg003 → seg004 → seg005）

## 输入要求
- **文件格式**：.mat格式文件
- **文件命名**：`dataX_5min_segXXX_tt_yes_XX.mat`
- **数据结构**：包含timetable格式的变量
- **时间格式**：duration类型的时间戳

## 输出结果
- **保存位置**：`2、Processed data` 文件夹
- **文件命名**：`merged_dataX_all_segments_ttY_YYYYMMDD_HHMMSS.mat`
- **变量命名**：`merged_tt1` 或 `merged_tt2`

## 使用方法

### 1. 准备数据
确保所有需要合并的数据文件都放在 `1、Raw data` 文件夹中，文件命名符合规范。

### 2. 运行脚本
在MATLAB中运行：
```matlab
merge_data_files
```

或者在命令行中运行：
```bash
matlab -batch "merge_data_files"
```

### 3. 查看结果
脚本运行完成后，合并的数据文件将保存在 `2、Processed data` 文件夹中。

## 运行示例

### 输入文件列表
```
1、Raw data/
├── data3_5min_seg001_tt_yes_15.mat
├── data3_5min_seg002_tt_yes_10.mat
├── data3_5min_seg003_tt_yes_11.mat
├── data3_5min_seg004_tt_yes_14.mat
├── data3_5min_seg005_tt_yes_16.mat
├── data4_5min_seg001_tt_yes_13.mat
├── data4_5min_seg002_tt_yes_13.mat
├── data4_5min_seg003_tt_yes_11.mat
├── data4_5min_seg004_tt_yes_12.mat
└── data4_5min_seg005_tt_yes_15.mat
```

### 输出结果
```
2、Processed data/
├── merged_data3_all_segments_tt1_20250826_103312.mat
└── merged_data3_all_segments_tt2_20250826_103313.mat
```

### 控制台输出示例
```
=== 数据文件合并脚本 ===
扫描文件夹: 1、Raw data
找到 10 个文件
文件排序结果:
  1. data3_5min_seg001_tt_yes_15.mat (data3, seg001)
  2. data3_5min_seg002_tt_yes_10.mat (data3, seg002)
  ...

=== 处理变量类型: data3_5min_seg001_tt1 ===
处理文件 1/10: data3_5min_seg001_tt_yes_15.mat
  - 数据点: 154200, 时长: 60.0秒
...

合并完成！
输出文件: merged_data3_all_segments_tt1_20250826_103312.mat
总数据点: 1541998
总时长: 600.0秒
时间范围: 0秒 到 600秒
```

## 数据验证

### 验证合并结果
```matlab
% 加载合并后的数据
load('2、Processed data/merged_data3_all_segments_tt1_20250826_103312.mat');

% 查看数据结构
whos merged_tt1

% 显示前几行数据
disp(merged_tt1(1:5,:));

% 检查时间连续性
time_vector = merged_tt1.Properties.RowTimes;
fprintf('时间范围: %s 到 %s\n', char(time_vector(1)), char(time_vector(end)));
fprintf('总时长: %.1f秒\n', seconds(time_vector(end) - time_vector(1)));
```

## 技术细节

### 时间处理机制
1. **第一个文件**：时间归零，从0秒开始
2. **后续文件**：在前一个文件结束时间基础上继续
3. **采样间隔**：假设采样频率为2570Hz，时间间隔约为0.000389秒

### 变量识别逻辑
- 自动识别文件中的所有timetable变量
- 根据变量名模式匹配对应的tt1或tt2类型
- 支持不同文件中变量名前缀的变化

## 注意事项
1. 确保所有输入文件的数据格式一致
2. 文件命名必须符合规范格式
3. 时间表必须包含有效的时间戳
4. 建议在处理大量数据前先进行小规模测试

## 故障排除

### 常见错误
1. **文件格式不匹配**：检查文件命名是否符合规范
2. **找不到timetable变量**：确认文件中包含正确的时间表数据
3. **时间格式错误**：检查时间戳是否为duration类型

### 调试建议
- 使用MATLAB调试器逐步执行脚本
- 检查中间变量的值和结构
- 验证单个文件的数据格式

## 版本信息
- **版本**：1.0
- **创建日期**：2025-08-26
- **兼容性**：MATLAB R2018b及以上版本
